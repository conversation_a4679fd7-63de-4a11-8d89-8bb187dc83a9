﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LinCom.Imp
{
    internal interface ICommonCode
    {
        string GenerateSlug(string input);
        string RemoveDiacritics(string text);
        string HasherMotDePasse(string motDePasse);
        bool VerifierMotDePasse(string motDePasseEntree, string motDePasseHache);
        string GenererToken();
        string GetRelativeDate(DateTime date);

    }
}
