﻿using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
   
    public class ReplyForumImp : IReplyForum
    {
        int msg;
        RepliesForum p = new RepliesForum();
        public int add(ReplyForum_Class add)
        {
            using (Connection con = new Connection())
            {

                p.SujetForumId = add.SujetForumId;
                p.MembreId = add.MembreId;
                p.Contenu = add.Contenu;
                p.DateReply = add.DateReply;
                p.name = add.name;
                try
                {
                    con.RepliesForums.Add(p);

                    if (con.SaveChanges() == 1)
                    {
                        con.RepliesForums.Add(p);
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }


            }
            return msg;
        }

        public void afficherDetails(int code, ReplyForum_Class pr)
        {
            using (Connection con = new Connection())
            {
                p = con.RepliesForums.Where(x => x.RepliesForumId == code).FirstOrDefault();

                if (p != null)
                {

                    pr.SujetForumId = p.SujetForumId;
                    pr.MembreId = p.MembreId;
                    pr.Contenu = p.Contenu;
                    pr.DateReply = p.DateReply;
                    pr.name = p.name;

                }

            }
        }

        public void afficherDetails(string code, ReplyForum_Class pr)
        {
            throw new NotImplementedException();
        }

        public void Chargement_GDV(GridView GV_apv)
        {
            using (Connection con = new Connection())
            {

                var obj = (from ep in con.RepliesForums
                           join a in con.SujetForums on ep.SujetForumId equals a.SujetForumId
                           join e in con.Membres on ep.MembreId equals e.MembreId
                           select new
                           {


                               SujetForm = ep.SujetForumId,
                               membre = ep.MembreId,
                               contenu = ep.Contenu,

                               date = ep.DateReply,
                               name = ep.name


                           }).ToList();

                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }
        }

        public void chargerReplyFroum(DropDownList lst)
        {
            throw new NotImplementedException();
        }

        public int count()
        {
            int n = 0;
            using (Connection con = new Connection())
            {
                var b = (from l in con.Provinces
                         select l).Count();
                n = b;
            }
            return n;
        }

        public int edit(ReplyForum_Class cl, int id)
        {
            using (Connection con = new Connection())
            {
                p = con.RepliesForums.Where(x => x.RepliesForumId == id).FirstOrDefault();

                try
                {
                    p.SujetForumId = cl.SujetForumId;
                    p.MembreId = cl.MembreId;
                    p.Contenu = cl.Contenu;
                    p.DateReply = cl.DateReply;
                    p.name = cl.name;


                    if (con.SaveChanges() == 1)
                    {
                        con.RepliesForums.Add(p);
                        con.Entry(p).State = EntityState.Modified;
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }
                return msg;
            }
        }

        public void search(GridView GV_apv, string code)
        {
            using (Connection con = new Connection())
            {



                var obj = (from ep in con.RepliesForums
                           join a in con.SujetForums on ep.SujetForumId equals a.SujetForumId
                           join e in con.Membres on ep.MembreId equals e.MembreId
                           where (ep.MembreId.ToString().Contains(code) && ep.SujetForumId.ToString().Contains(code))
                           select new
                           {

                               SujetForm = ep.SujetForumId,
                               membre = ep.MembreId,
                               contenu = ep.Contenu,

                               date = ep.DateReply,
                               name = ep.name

                           }).ToList();


                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }
        }

        public int supprimer(int id)
        {
            using (Connection con = new Connection())
            {

                p = con.RepliesForums.Where(x => x.RepliesForumId == id).First();

                if (con.Entry(p).State == EntityState.Detached)
                    con.RepliesForums.Attach(p);

                con.RepliesForums.Remove(p);
                con.SaveChanges();

                return msg = 1;
            }
        }

     
    }
}