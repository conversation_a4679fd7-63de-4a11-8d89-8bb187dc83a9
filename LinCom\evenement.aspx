﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="evenement.aspx.cs" Inherits="LinCom.evenement" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Page Title -->
    <div class="page-title">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h2 class="display-5 fw-bold">Événements Internationaux</h2>
                        <p class="lead">Découvrez nos conférences, forums et sommets dédiés à l'engagement et au leadership des jeunes à travers le monde.</p>
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="home.aspx">Accueil</a></li>
                    <li><a href="#">Événements</a></li>
                    <li class="current">Internationaux</li>
                </ol>
            </div>
        </nav>
    </div>

    <!-- Filtres -->
   
   <main class="main">
  <section class="events py-5 bg-light">
    <div class="container">
      <div class="row g-5">

        <!-- Colonne gauche : Événements -->
        <div class="col-lg-8">
          <div class="row g-4">

              <asp:ListView ID="listevent" runat="server" OnItemCommand="listevent_ItemCommand">
    <EmptyItemTemplate>Aucune donnée pour le moment.</EmptyItemTemplate>
    <ItemTemplate>
              <div class="col-md-6">
  <div class="card border-0 shadow-lg h-100 rounded-4 overflow-hidden">
    <img src='<%# HttpUtility.HtmlEncode(string.Concat("../file/post/",Eval("photo"))) %>' style="height: 220px; object-fit: cover;" alt="Événement">
    <div class="card-body d-flex flex-column">
      <h5 class="card-title fw-bold">
        
           <asp:LinkButton runat="server" CommandName="viewevent" CommandArgument='<%# Eval("name") %>'><%# HttpUtility.HtmlEncode( Eval("Titre")) %></asp:LinkButton>
                       
      </h5>
    
      <div class="mt-auto">
        <div class="d-flex justify-content-between align-items-center fw-semibold mb-3 text-event">
          <span><i class="bi bi-calendar-event"></i> <%# Convert.ToDateTime( Eval("DatePublication")).ToString("dd/MM/yyyy") %></span>
          <span><i class="bi bi-geo-alt-fill"></i> <%# Eval("eventplace") %></span>
          <span><i class="bi bi-person-bounding-box"></i></i> <%# Eval("author") %></span>
        </div>
        <a href='<%# Eval("lien_isncription") %>' class="btn btn-event w-100 rounded-pill fw-semibold text-white" target="_blank">
          <i class="bi bi-box-arrow-in-right me-1"></i>S’inscrire
        </a>
      </div>
    </div>
    <div class="position-absolute top-0 start-0 m-3 badge bg-danger rounded-pill text-white"><%# Eval("externevent") %></div>
  </div>
</div>
            </ItemTemplate>
</asp:ListView>

            
            <!-- Autres Event Cards identiques à dupliquer... -->

          </div>

          <!-- Pagination -->
          <nav class="mt-5 d-flex justify-content-center">
            <ul class="pagination pagination-rounded gap-2 mb-0">
              <li class="page-item"><a class="page-link" href="#"><i class="bi bi-chevron-left"></i></a></li>
              <li class="page-item active"><a class="page-link" href="#">1</a></li>
              <li class="page-item"><a class="page-link" href="#">2</a></li>
              <li class="page-item"><a class="page-link" href="#"><i class="bi bi-chevron-right"></i></a></li>
            </ul>
          </nav>
        </div>

        <!-- Colonne droite : Recherche, récents, publicité -->
        <div class="col-lg-4">
          <!-- Recherche -->
          <div class="card mb-4 shadow-sm rounded-4 p-3">
            <h5 class="fw-bold mb-3">Recherche</h5>
            <div class="d-grid gap-3">
              <select class="form-select rounded-pill">
                <option value="">Type d'événement</option>
                <option>Conférence</option>
                <option>Forum</option>
                <option>Atelier</option>
              </select>
              <input type="date" class="form-control rounded-pill">
              <button class="btn btn-primary rounded-pill fw-semibold">Filtrer</button>
            </div>
          </div>

          <!-- Événements récents -->
          <div class="card mb-4 shadow-sm rounded-4 p-3">
            <h5 class="fw-bold mb-3">Domaines d'Intervention</h5>
             <!-- Categories Widget -->
 <div class="categories-widget widget-item">

     <ul class="mt-3">
         <asp:ListView ID="listcategorie" runat="server" OnItemCommand="listcategorie_ItemCommand">
             <EmptyItemTemplate>Aucune Donnée</EmptyItemTemplate>
             <ItemTemplate>
                 <li>
                     <asp:LinkButton runat="server" CommandName="viewdom" CommandArgument='<%# Eval("Id") %>'>
                         <%#  HttpUtility.HtmlEncode(Eval("Libelle")) %>
                         <span>(<b><%#  HttpUtility.HtmlEncode(Eval("NombrePosts")) %> </b>)</span>

                     </asp:LinkButton>

                 </li>

             </ItemTemplate>
         </asp:ListView>

     </ul>

 </div>
 <!--/Categories Widget -->
          </div>

          <!-- Publicité -->
         <!-- Publicité - Carrousel -->
<div class="bg-white shadow-sm p-4 rounded-4">
  <h5 class="fw-bold mb-3 text-center">Publicité</h5>
  <div id="carouselPub" class="carousel slide" data-bs-ride="carousel">
    <div class="carousel-inner rounded">
      <div class="carousel-item active">
        <img src="assets/img/blog/skills.png" class="d-block w-100" alt="Ad 1">
      </div>
      <div class="carousel-item">
        <img src="assets/img/blog/skills.png" class="d-block w-100" alt="Ad 2">
      </div>
      <div class="carousel-item">
        <img src="assets/img/blog/skills.png" class="d-block w-100" alt="Ad 3">
      </div>
    </div>
    <button class="carousel-control-prev" type="button" data-bs-target="#carouselPub" data-bs-slide="prev">
      <span class="carousel-control-prev-icon bg-dark rounded-circle" aria-hidden="true"></span>
      <span class="visually-hidden">Précédent</span>
    </button>
    <button class="carousel-control-next" type="button" data-bs-target="#carouselPub" data-bs-slide="next">
      <span class="carousel-control-next-icon bg-dark rounded-circle" aria-hidden="true"></span>
      <span class="visually-hidden">Suivant</span>
    </button>
  </div>
</div>

        </div>

      </div>
    </div>
  </section>
</main>

   <!-- STYLES -->
<style>
  .custom-btn {
    background-color: #148D7F;
    border-color: #148D7F;
    color: white;
  }

  .custom-btn:hover {
    background-color: #117066;
    border-color: #117066;
  }

  .event-card:hover {
    transform: translateY(-6px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
  }

  .pagination .page-link {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    text-align: center;
    line-height: 40px;
  }

  .pagination .active .page-link {
    background-color: #148D7F;
    border-color: #148D7F;
    color: white;
  }
  .text-event {
    color: #008374 !important;
  }

  .btn-event {
    background-color: #008374;
    border-color: #008374;
  }

  .btn-event:hover {
    background-color: #0a5e54;
    border-color: #0a5e54;
  }
</style>
</asp:Content>
