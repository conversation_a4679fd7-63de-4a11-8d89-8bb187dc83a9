﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="ongorg.aspx.cs" Inherits="LinCom.ongorg" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
  
     <main class="container py-5">
                 <!-- Page Title -->
<div class="page-title" runat="server" >
    <div class="heading">
        <div class="container">
            <div class="row d-flex justify-content-center text-center">
                <div class="col-lg-8">
                    <h1 runat="server" id="txttitle">Blog Details</h1>
                    <p class="mb-0">Odio et unde deleniti. Deserunt numquam exercitationem. Officiis quo odio sint voluptas consequatur ut a odio voluptatem. Sit dolorum debitis veritatis natus dolores. Quasi ratione sint. Sit quaerat ipsum dolorem.</p>
                </div>
            </div>
        </div>
    </div>
    <nav class="breadcrumbs">
        <div class="container">
            <ol>
                <li><a href="home.aspx">Home</a></li>
                <li><a href="blogong.aspx">Article</a></li>
                <li class="current" runat="server" id="txttitle3">Detail de l'article</li>
            </ol>
        </div>
    </nav>
</div>
<!-- End Page Title -->
    <!-- Header avec logo et infos -->
    <section class="py-5 bg-light">
        <div class="container text-center">
            <img src="file/organ/default-logo.png" alt="Logo Organisation" class="mb-3 rounded-circle shadow" style="width: 120px; height: 120px; object-fit: cover;">
            <h2 class="fw-bold">Nom de l'Organisation</h2>
            <p class="text-muted">SIGLE • Téléphone : +243 999 999 999 • Email : <EMAIL></p>
            <p class="text-muted">Adresse : Avenue X, Commune Y, Province Z</p>
            <div class="d-flex justify-content-center gap-3 mt-2">
                <a href="#"><i class="bi bi-geo-alt-fill"></i> Carte GPS</a>
                <a href="#"><i class="bi bi-globe"></i> Site Web</a>
                <a href="#"><i class="bi bi-facebook"></i></a>
                <a href="#"><i class="bi bi-twitter-x"></i></a>
                <a href="#"><i class="bi bi-linkedin"></i></a>
            </div>
        </div>
    </section>

    <!-- Vision et mission -->
    <section class="py-4">
        <div class="container">
            <h4 class="fw-bold">Vision & Mission</h4>
            <p>Notre vision est de devenir un acteur majeur dans le développement humain. Notre mission est de soutenir les communautés locales par des projets éducatifs, environnementaux et sociaux.</p>
        </div>
    </section>

    <!-- Domaines d'intervention -->
    <section class="py-4 bg-light">
        <div class="container">
            <h4 class="fw-bold">Domaines d'intervention</h4>
            <div class="d-flex flex-wrap gap-2">
                <span class="badge bg-primary">Éducation</span>
                <span class="badge bg-success">Santé</span>
                <span class="badge bg-warning text-dark">Environnement</span>
                <span class="badge bg-info text-dark">Autonomisation des femmes</span>
            </div>
        </div>
    </section>

    <!-- Services et produits -->
    <section class="py-4">
        <div class="container">
            <h4 class="fw-bold">Services & Produits</h4>
            <div class="row g-4">
                <div class="col-md-6">
                    <div class="p-3 bg-light border rounded shadow-sm h-100">
                        <h5>Services</h5>
                        <ul class="mb-0">
                            <li>Formations professionnelles</li>
                            <li>Accompagnement des jeunes entrepreneurs</li>
                            <li>Consultations en santé communautaire</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="p-3 bg-light border rounded shadow-sm h-100">
                        <h5>Produits</h5>
                        <ul class="mb-0">
                            <li>Kits scolaires</li>
                            <li>Outils agricoles écologiques</li>
                            <li>Supports éducatifs numériques</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Membres (carrousel) -->
    <section class="py-5 bg-light">
        <div class="container">
            <h4 class="fw-bold mb-4">Membres</h4>
            <div id="carouselMembres" class="carousel slide" data-bs-ride="carousel">
                <div class="carousel-inner text-center">
                    <div class="carousel-item active">
                        <img src="file/membre1.jpg" class="rounded-circle shadow" style="width: 100px; height: 100px;">
                        <p class="mt-2">Mme. Amina</p>
                    </div>
                    <div class="carousel-item">
                        <img src="file/membre2.jpg" class="rounded-circle shadow" style="width: 100px; height: 100px;">
                        <p class="mt-2">Mr. Jean</p>
                    </div>
                </div>
                <button class="carousel-control-prev" type="button" data-bs-target="#carouselMembres" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon"></span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#carouselMembres" data-bs-slide="next">
                    <span class="carousel-control-next-icon"></span>
                </button>
            </div>
        </div>
    </section>

    <!-- Activités et événements (carrousel) -->
    <section class="py-5">
        <div class="container">
            <h4 class="fw-bold mb-4">Activités & Événements</h4>
            <div id="carouselActivites" class="carousel slide" data-bs-ride="carousel">
                <div class="carousel-inner">
                    <div class="carousel-item active">
                        <div class="card">
                            <img src="file/event1.jpg" class="card-img-top" alt="Événement 1">
                            <div class="card-body">
                                <h5 class="card-title">Atelier de Formation</h5>
                                <p class="card-text">Formation sur l'entrepreneuriat à Goma.</p>
                            </div>
                        </div>
                    </div>
                    <div class="carousel-item">
                        <div class="card">
                            <img src="file/event2.jpg" class="card-img-top" alt="Événement 2">
                            <div class="card-body">
                                <h5 class="card-title">Sensibilisation Environnementale</h5>
                                <p class="card-text">Campagne de nettoyage urbain.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <button class="carousel-control-prev" type="button" data-bs-target="#carouselActivites" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon"></span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#carouselActivites" data-bs-slide="next">
                    <span class="carousel-control-next-icon"></span>
                </button>
            </div>
        </div>
    </section>

    <!-- Blocs Projets & Partenaires -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row g-4">
                <div class="col-md-6">
                    <div class="bg-white p-4 shadow rounded h-100">
                        <h5 class="fw-bold">Projets</h5>
                        <ul>
                            <li>Projet Alpha : Construction d'écoles</li>
                            <li>Projet Beta : Accès à l’eau potable</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="bg-white p-4 shadow rounded h-100">
                        <h5 class="fw-bold">Partenaires</h5>
                        <ul>
                            <li>UNICEF</li>
                            <li>OXFAM</li>
                            <li>Ambassade de France</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Formulaire de contact -->
    <section class="py-5">
        <div class="container">
            <h4 class="fw-bold">Contacter l'organisation</h4>
            <form>
                <div class="row g-3">
                    <div class="col-md-6">
                        <input type="text" class="form-control" placeholder="Votre nom">
                    </div>
                    <div class="col-md-6">
                        <input type="email" class="form-control" placeholder="Votre email">
                    </div>
                    <div class="col-12">
                        <textarea class="form-control" rows="4" placeholder="Votre message..."></textarea>
                    </div>
                    <div class="col-12 text-end">
                        <button class="btn btn-primary">Envoyer</button>
                    </div>
                </div>
            </form>
        </div>
    </section>
</main>

</asp:Content>
