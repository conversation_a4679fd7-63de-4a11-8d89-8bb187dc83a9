﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.file;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class ressource : System.Web.UI.Page
    {
        private int info;
        Membre_Class membreClass = new Membre_Class();
        Membre_Class memb = new Membre_Class();
        Membre_Class mem = new Membre_Class();
        IMembre objMembre = new MembreImp();
        IProvince objProvince = new ProvinceImp();
        ICommune objCommune = new CommuneImp();
        ICommonCode co = new CommonCode();
        RoleMembre_Class rl = new RoleMembre_Class();
        IRoleMembre objrl = new RoleMembreImp();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();

        IRessource obj = new RessourceImp();
        Ressources_Class pos = new Ressources_Class();
        Ressources_Class post = new Ressources_Class();
        Ressources_Class p = new Ressources_Class();

        AvisRessource_Class com = new AvisRessource_Class();
        IAvisRessource objcom = new AvisRessourceImp();

        DomaineRessource_Class actco = new DomaineRessource_Class();
        IDomaineRessource objactco = new DomaineRessourceImp();

        DomaineInterventionOrganisation_Class domai = new DomaineInterventionOrganisation_Class();
        IDomaineInterventionOrganisation objdom = new DomaineInterventionOrganisationImp();

        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();

        CategoriePost_Class catpost = new CategoriePost_Class();
        ICategoryPost objcatpost = new CategoriePostImp();

        static string imge, imge1, pdfe, nameorg;
        static long ide, idorg;
        static int rolid;
        long index;

        static string slug;
        protected void Page_Load(object sender, EventArgs e)
        {

            if (!IsPostBack)
            {
                slug = Request.QueryString["name"];

                if (slug == null)
                {
                    Response.Redirect("~/ressources.aspx");
                }
                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {//admin
                    ide = Convert.ToInt64(idperso.Value);//idconnecte

                }


                //if (slug != null)
                //{
                initial_msg();
                sharefacebook();
                hfUrlPage.Value = Request.Url.AbsoluteUri;
                //LoadString();

                //}
                //else Response.Redirect("~/news.aspx");



            }
        }
        public string GetRelativeDate(DateTime date)
        {
            return co.GetRelativeDate(date);
        }

        public void sharefacebook()
        {
            obj.AfficherDetails(slug, -1, 1, post);//appel du post

            string titre = HttpUtility.HtmlEncode(post.Titre);
            string description = HttpUtility.HtmlEncode(post.Description);
            string image = ResolveUrl("~/file/ressourc/" + post.photocouverture);
            string pdf = ResolveUrl("~/file/ressourc/" + post.Fichier);
            string fullImageUrl = $"{Request.Url.Scheme}://{Request.Url.Authority}{image}";
            string url = Request.Url.AbsoluteUri;

            LiteralControl ogTags = new LiteralControl($@"
    <meta property='og:title' content='{titre}' />
    <meta property='og:description' content='{description}' />
    <meta property='og:image' content='{fullImageUrl}' />
    <meta property='og:url' content='{url}' />
    <meta property='og:type' content='article' />
    <meta name='twitter:card' content='summary_large_image' />
    <meta name='twitter:title' content='{titre}' />
    <meta name='twitter:description' content='{description}' />
    <meta name='twitter:image' content='{fullImageUrl}' />
");

            Page.Header.Controls.Add(ogTags);

        }
        void commentaire()
        {

            try
            {
                if (txtmessage.Text == "")
                {
                    Response.Write("<script LANGUAGE=JavaScript>alert('Veuillez renseigner  commentaire.!!!')</script>");

                }
                else
                {


                    obj.AfficherDetails(slug, -1, 1, p);//appel du post

                    com.RessourceId = Convert.ToInt64(p.RessourceId);
                    com.MembreId = ide;
                    com.Commentaire = txtmessage.Text;
                    com.DateAvis = DateTime.Now.ToString();
                    com.statut = "vu";
                   
                    info = objcom.Ajout(com);

                    if (info > 0)
                    {
                        objcom.ChargementListview(listCommentaires, p.RessourceId, -1, "", "vu", 1);
                        //  txtcomment.InnerText = objcom.count(-1, Convert.ToInt64(p.PostId), 0) + " Commentaire(s)";
                        txtcommentpost.InnerText = objcom.count(-1, Convert.ToInt64(p.RessourceId),"vu", 0) + " Commentaire(s)";

                        txtmessage.Text = "";
                        Response.Write("<script LANGUAGE=JavaScript>alert('Votre commentaire a été bien envoyé')</script>");

                    }
                    else
                    {
                        Response.Write("<script LANGUAGE=JavaScript>alert('Problème Technique.!!!')</script>");

                    }

                }
            }
            catch (Exception e)
            {

            }
        }
        //protected void OnPagePropertiesChanging(object sender, PagePropertiesChangingEventArgs e)
        //{
        //    (listblog.FindControl("DataPager1") as DataPager).SetPageProperties(e.StartRowIndex, e.MaximumRows, false);
        //    obj.Chargement_GDV(listblog, 0, "blog", 7);
        //}
        protected void listpost_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            //string index = e.CommandArgument.ToString();
            //if (e.CommandName == "view")
            //{

            //    obj.AfficherDetailsname(index, -1, 1, pos);
            //    post.number_of_view = pos.number_of_view + 1;
            //    obj.MiseajourData(post, pos.PostId, -1, 0);

            //    Response.Redirect("~/program.aspx?name=" + index);
            //}
        }

        void initial_msg()
        {

            //blog
            obj.Chargement_Listview(listRessourcesSimilaires, -1, "publié", 0);

            // obj.Chargement_GDV(listprogram, -1, -1, "programme", "publié", 1);
            // obj.Chargement_GDV(listprogram, -1, -1, "programme", "fnuap", 1);

            //blog
            //  obj.Chargement_GDV(listblog, 0, "blog", 3);
            //event
            // obj.Chargement_GDV(listevent, 0, "blog", 4);
            if (!string.IsNullOrEmpty(slug))
            {

                // obj.afficherDetails(Convert.ToInt32(nscno), "blog", pc);
                obj.AfficherDetails(slug, -1, 2, p);//appel du post


                // objcom.Chargement_GDV(listcomment, 0, "", Convert.ToInt64(nscno), 0);
                // obj.Chargement_GDV(listv1, (int)pc.IDCAT, 1);
                txttitle.InnerText = p.Titre;
                txtdescription.InnerHtml = p.Description;

                //   txttitle1.InnerText = pc.titre;
                txtdate.InnerText = Convert.ToDateTime(p.DatePublication).ToString("dd/MM/yyyy") + ", publié " + GetRelativeDate( Convert.ToDateTime(p.DatePublication));
                objOrganisation.AfficherDetails(Convert.ToInt64(p.AuteurId),org);
                txtauteur.InnerText = org.Nom;
                txttype.InnerText = p.typeressources;

               // imgauteur.Src = "~/file/program/" + p.video;
                txtcommentpost.InnerText = objcom.count(-1, Convert.ToInt64(p.RessourceId),"vu", 0) + " Commentaire(s)";
                // sectblogdetail.Attributes.Add("style", "background-image(~/file/site/)"+ pc.video);
                // sectblogdetail.Style["background-image"] = "~/file/site/"+p.video;
                //sectblogdetail.Style["background-image"] = "url(../file/post/"+p.photo+")";
                linkfichier.HRef = "~/file/ressourc/" + p.Fichier;
                sectblogdetail.Style["background-image"] = Page.ResolveUrl("~/file/ressourc/" + p.photocouverture);

                objOrganisation.AfficherDetails(Convert.ToInt64(p.OrganisationId), org);

                // nbrecomment.InnerText = objcom.count("", Convert.ToInt32(nscno), 0) + " Commentaire(s)";

                // objactdom.ChargerDomaines(listcategorie, "article");
                objactco.Chargement_Listview(listdompost,"actif",p.RessourceId,-1,-1,0);
                objcom.ChargementListview(listCommentaires, p.RessourceId, -1, "", "vu", 1);
                // objtabl.Chargement_GDV(gdvtag, Convert.ToInt64(nscno), 0);

            }
        }
        protected void btn_srch_Click(object sender, EventArgs e)
        {
            //if (txt_srch.Value == "")
            //    obj.Chargement_GDV(listv, 0, "blog", 5);
            //else obj.searchlist(listv, "blog", 0, 0, txt_srch.Value);
        }

        protected void voirtouract_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/news.aspx");
        }

        protected void btnEnvoyer_Click(object sender, EventArgs e)
        {
            if (ide >0)
            {
                commentaire();
            }
            else Response.Redirect("~/login.aspx");
        }

        protected void listRessourcesSimilaires_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            string index = e.CommandArgument.ToString();
            if (e.CommandName == "viewress" || e.CommandName == "viewressour")
            {
                obj.AfficherDetails(index, -1, 2, pos);
                post.nbrevue = pos.nbrevue + 1;
                obj.MiseajourData(post, pos.RessourceId, -1, 0);

                Response.Redirect("~/ressource.aspx?name=" + index);



            }
        }

        protected void voirtouractblog_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/newsupcome.aspx");
        }
        protected void bntcomment_Click(object sender, EventArgs e)
        {
          
            //nbrecomment.InnerText = objcom.count("", Convert.ToInt32(nscno), 0) + " Commentaire(s)";

        }


        protected void listcategorie_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            //int index = Convert.ToInt32(e.CommandArgument);
            //if (e.CommandName == "viewcat")
            //{
            //    princip.Visible = false;
            //    second.Visible = true;

            //    obj.Chargement_GDV(listblog, index, "blog", 1);

            //}
        }

        protected void btnenreg_ServerClick(object sender, EventArgs e)
        {
            if (slug != null && ide > 0)
            {
                commentaire();
            }
            else Response.Redirect("~/login.aspx");
        }
    }
}