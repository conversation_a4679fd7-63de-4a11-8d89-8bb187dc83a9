﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface INotification
    {
        void AfficherDetails(long notificationId, Notification_Class notification);
        int Ajouter(Notification_Class notification);
        int Modifier(Notification_Class notification);
        int Supprimer(long notificationId);
        void ChargerNotifications(GridView gdv, long membreId, bool nonLues = false);
        int MarquerCommeLue(long notificationId);
        void ChargerNotificationsParType(GridView gdv, string type);
        int EnvoyerNotificationGroupe(Notification_Class notification, long[] membreIds);
        void ChargerStatistiques(Repeater rpt, long membreId);
        void PurgerAnciennesNotifications(int joursConservation);
    }
}
