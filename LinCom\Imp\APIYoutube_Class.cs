﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Script.Serialization;

namespace LinCom.Imp
{
    public class APIYoutube_Class
    {
   
    public class YouTubeVideo
    {
        public string title { get; set; }
        public string videoId { get; set; }
        public string description { get; set; }
        public string thumbnail { get; set; }
    }

    public List<YouTubeVideo> GetVideosFromYouTube(string apikey,string channelID)
        {
            string apiKey = apikey; // Remplace par ta vraie clé : TA_CLE_API
            string channelId = channelID; // Remplace par le vrai Channel ID : ID_DE_LA_CHAINE
            string apiUrl = $"https://www.googleapis.com/youtube/v3/search?key={apiKey}&channelId={channelId}&part=snippet&type=video&maxResults=6&order=date";

            using (WebClient client = new WebClient())
            {
                string json = client.DownloadString(apiUrl);
                var serializer = new JavaScriptSerializer();
                dynamic result = serializer.DeserializeObject(json);
                List<YouTubeVideo> videos = new List<YouTubeVideo>();

                foreach (var item in result["items"])
                {
                    YouTubeVideo video = new YouTubeVideo
                    {
                        title = item["snippet"]["title"],
                        videoId = item["id"]["videoId"],
                        description = item["snippet"]["description"],
                        thumbnail = item["snippet"]["thumbnails"]["high"]["url"]
                    };
                    videos.Add(video);
                }

                return videos;
            }
        }
    }
} 