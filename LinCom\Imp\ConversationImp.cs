﻿using LinCom.Class;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class ConversationImp : IConversation
    {
        int msg;
        private Conversation conversation = new Conversation();

        public void AfficherDetails(long conversationId, Conversation_Class conversationClass)
        {
            using (Connection con = new Connection())
            {
                var c = con.Conversations.FirstOrDefault(x => x.ConversationId == conversationId);
                if (c != null)
                {
                    conversationClass.ConversationId = c.ConversationId;
                    conversationClass.Sujet = c.Sujet;
                    conversationClass.name = c.name;
                }
            }
        }

        public int AjouterParticipant(long conversationId, long membreId)
        {
            using (Connection con = new Connection())
            {
                if (!con.ParticipantConversations.Any(p =>
                    p.ConversationId == conversationId && p.MembreId == membreId))
                {
                    var participant = new ParticipantConversation
                    {
                        ConversationId = conversationId,
                        MembreId = membreId,

                    };

                    try
                    {
                        con.ParticipantConversations.Add(participant);
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public int Supprimer(long conversationId)
        {
            using (Connection con = new Connection())
            {
                var c = con.Conversations.FirstOrDefault(x => x.ConversationId == conversationId);
                if (c != null)
                {
                    con.Conversations.Remove(c);
                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public void ChargerConversations(DropDownList ddl, long membreId, string name = "")
        {
            using (Connection con = new Connection())
            {
                var query = from c in con.Conversations
                            join p in con.ParticipantConversations on c.ConversationId equals p.ConversationId
                            where p.MembreId == membreId
                            select new
                            {
                                c.ConversationId,
                                c.Sujet,
                                c.name
                            };

                if (!string.IsNullOrEmpty(name))
                {
                    query = query.Where(x => x.name == name);
                }

                ddl.DataSource = query.ToList();
                ddl.DataTextField = "Sujet";
                ddl.DataValueField = "ConversationId";
                ddl.DataBind();
            }
        }

        public void ChargerParticipants(DropDownList ddl, long conversationId)
        {
            using (Connection con = new Connection())
            {
                var participants = from p in con.ParticipantConversations
                                   join m in con.Membres on p.MembreId equals m.MembreId
                                   where p.ConversationId == conversationId
                                   select new
                                   {
                                       m.MembreId,
                                       NomComplet = m.Nom + " " + m.Prenom,
                                   };

                ddl.DataSource = participants.ToList();
                ddl.DataTextField = "NomComplet";
                ddl.DataValueField = "MembreId";
                ddl.DataBind();
            }
        }

        public int Creer(Conversation_Class conversationClass)
        {
            using (Connection con = new Connection())
            {
                conversation.ConversationId = conversationClass.ConversationId;
                conversation.Sujet = conversationClass.Sujet;
                conversation.name = conversationClass.name;

                try
                {
                    con.Conversations.Add(conversation);
                    return con.SaveChanges();
                }
                catch
                {
                    return 0;
                }
            }
        }

        public int Modifier(Conversation_Class conversationClass)
        {
            using (Connection con = new Connection())
            {
                var c = con.Conversations.FirstOrDefault(x => x.ConversationId == conversationClass.ConversationId);
                if (c != null)
                {
                    c.Sujet = conversationClass.Sujet;
                    c.name = conversationClass.name;

                    try
                    {
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }

        public int RetirerParticipant(long conversationId, long membreId)
        {
            using (Connection con = new Connection())
            {
                var participant = con.ParticipantConversations.FirstOrDefault(p =>
                    p.ConversationId == conversationId && p.MembreId == membreId);

                if (participant != null)
                {
                    try
                    {
                        con.ParticipantConversations.Remove(participant);
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }

        public void ChargerParticipants(Repeater rpt, long conversationId)
        {
            using (Connection con = new Connection())
            {
                var participants = from p in con.ParticipantConversations
                                   join m in con.Membres on p.MembreId equals m.MembreId
                                   where p.ConversationId == conversationId
                                   select new
                                   {
                                       m.MembreId,
                                       NomComplet = m.Nom + " " + m.Prenom,
                                      
                                   };

                rpt.DataSource = participants.ToList();
                rpt.DataBind();
            }
        }

        public bool VerifierParticipation(long conversationId, long membreId)
        {
            using (Connection con = new Connection())
            {
                return con.ParticipantConversations.Any(p =>
                    p.ConversationId == conversationId && p.MembreId == membreId);
            }
        }
    }
}