﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class ressources : System.Web.UI.Page
    {

        Organisation_Class org = new Organisation_Class();
        Organisation_Class orga = new Organisation_Class();
        IOrganisation objorg = new OrganisationImp();
        IRessource objpost = new RessourceImp();
        Ressources_Class post = new Ressources_Class();
        Ressources_Class pos = new Ressources_Class();

        IDomaineRessource objdompost = new DomaineRessourceImp();
        DomaineRessource_Class dompost = new DomaineRessource_Class();

        Membre_Class mem = new Membre_Class();
        IMembre objmem = new MembreImp();
        IPartenaire objpart = new PartenaireImp();
        Partenaire_Class part = new Partenaire_Class();
        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();
        ICommonCode co = new CommonCode();
        //   UrlPartage = $"{Request.Url.GetLeftPart(UriPartial.Authority)}/post/" + ep.name,
        IDomaineInterventionOrganisation objdomorg = new DomaineInterventionOrganisationImp();
        DomaineInterventionOrganisation_Class domorg = new DomaineInterventionOrganisation_Class();

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                affich();

            }
        }
        void affich()
        {
            // objpost.Chargement_GDV(listpost, -1, -1, "programme", "publié", 1);
            objpost.Chargement_Listview(listresource,-1,"publié",0);


            // objc.Chargement_GDVL(listinst, 2);
        }
        protected void lvArticles_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            //if (e.Item.ItemType == ListViewItemType.DataItem)
            //{
            //    // Récupérer la ligne de données
            //    var row = (DataRowView)e.Item.DataItem;

            //    HiddenField hf = (HiddenField)e.Item.FindControl("hfPostId");
            //    long postId = Convert.ToInt64(hf.Value);

            //    // Trouver le contrôle enfant listdomai
            //    var listdomai = (ListView)e.Item.FindControl("listdomai");

            //    objdompost.ChargerListView(listdomai, postId, -1, 1, "");
            //}
        }

        public string GetRelativeDate(DateTime date)
        {
            return co.GetRelativeDate(date);
        }


        protected void listpost_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
           
        }

        protected void listevent_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            //string index = e.CommandArgument.ToString();
            //if (e.CommandName == "viewevent" || e.CommandName == "viewevent1")
            //{

            //    objpost.AfficherDetailsname(index, -1, 1, pos);
            //    post.number_of_view = pos.number_of_view + 1;
            //    objpost.MiseajourData(post, pos.PostId, -1, 0);

            //    Response.Redirect("~/event.aspx?name=" + index);



            //}
        }

        protected void listcategorie_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            //long index = Convert.ToInt64(e.CommandArgument);
            //if (e.CommandName == "viewdom")
            //{
            //    objpost.Chargement_GDV(listresource, index, -1, "article", "publié", 2);



            //}
        }
  
        protected void btnsearch_ServerClick(object sender, EventArgs e)
        {
            if (drpdtyperessources.SelectedValue == "-1")
                objpost.Chargement_Listview(listresource, -1, "publié", 0);
            else objpost.Chargement_Listview(listresource, -1, drpdtyperessources.SelectedValue, 1);

        }

        protected void listresource_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            string index = e.CommandArgument.ToString();
            if (e.CommandName == "view" || e.CommandName == "viewblog")
            {
                objpost.AfficherDetails(index, -1, 2, pos);
                post.nbrevue = pos.nbrevue + 1;
                objpost.MiseajourData(post, pos.RessourceId, -1, 0);

                Response.Redirect("~/ressource.aspx?name=" + index);



            }
        }

      
    }
}