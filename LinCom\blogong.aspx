﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="blogong.aspx.cs" Inherits="LinCom.blogong" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">


    <main class="main">

        <!-- Titre de la page Blog -->
        <div class="page-title">
            <div class="heading">
                <!-- Titre de la page -->
                <div class="page-title">
                    <nav class="breadcrumbs">
                        <div class="container">
                            <ol>
                                <li><a href="home.aspx">Accueil</a></li>
                                <li><a href="#">ONG</a></li>
                                <li class="current"><a href="ong.aspx">Liste des Organisations</a></li>
                            </ol>
                        </div>
                    </nav>
                </div>
                <!-- Fin Titre de la page -->
            </div>

        </div>

        <!-- Fin Titre de la page Blog -->

        <section class="blog-posts section">
            <div class="container">
                <div class="row">

                    <!-- Colonne gauche : Liste des activités -->
                    <div class="col-lg-8">
                        <div class="row gy-4">

                            <!-- Répéter ce bloc pour chaque activité -->
                            <asp:ListView ID="listpost" runat="server" OnItemCommand="listpost_ItemCommand">
                                <EmptyItemTemplate>Aucune Donnée</EmptyItemTemplate>
                                <ItemTemplate>
                                    <div class="col-md-12">
                                        <div class="activity-card">
                                            <div class="activity-header d-flex align-items-center">
                                                <img src='<%# HttpUtility.HtmlEncode( string.Concat("../file/organ/",Eval("Photoorg"))) %>' class="org-logo" alt="Logo organisation">
                                                <div class="activity-info ms-2">
                                                    <h5 class="org-name"><%# Eval("Nomorg") %></h5>
                                                    <small class="text-muted">Publié 
                                                      <asp:Label runat="server" Text='<%# GetRelativeDate(Convert.ToDateTime(Eval("DatePublication"))) %>' />


                                                        </time></small>
                                                </div>
                                            </div>
                                            <div class="activity-body mt-2">
                                                <p>
                                                    <strong>Titre :</strong>

                                                    <asp:LinkButton runat="server" class="activity-title" CommandName="view" CommandArgument='<%# Eval("name") %>'><%# HttpUtility.HtmlEncode( Eval("Titre")) %><i class="fa fa-arrow-right"></i></asp:LinkButton>

                                                </p>
                                                <p class="activity-snippet"><%# Eval("summery") %></p>
                                                <img src='<%# HttpUtility.HtmlEncode( string.Concat("../file/actualit/",Eval("photo"))) %>' class="activity-image" alt="Illustration">
                                            </div>
                                            <div class="activity-footer d-flex gap-2 flex-wrap">
                                               
                                                <asp:LinkButton runat="server" class="btn btn-light btn-sm" CommandName="viewblog" CommandArgument='<%# Eval("name") %>'>
     <i class="bi bi-chat-left-text"></i> Commenter

                                                </asp:LinkButton>


                                                <!-- Boutons de partage -->
                                                <%-- WhatsApp --%>
                                                <a class="btn btn-sm btn-light" target="_blank"
                                                    title="Partager sur WhatsApp"
                                                    href='<%# "https://api.whatsapp.com/send?text=" + HttpUtility.UrlEncode("Découvrez cet article : " + Eval("UrlPartage")) %>'>
                                                    <i class="bi bi-whatsapp"></i>
                                                </a>

                                                <%-- Facebook --%>
                                                <a class="btn btn-sm btn-light" target="_blank"
                                                    title="Partager sur Facebook"
                                                    href='<%# "https://www.facebook.com/sharer/sharer.php?u=" + HttpUtility.UrlEncode(Eval("UrlPartage").ToString()) %>'>
                                                    <i class="bi bi-facebook"></i>
                                                </a>

                                                <%-- LinkedIn --%>
                                                <a class="btn btn-sm btn-light" target="_blank"
                                                    title="Partager sur LinkedIn"
                                                    href='<%# "https://www.linkedin.com/sharing/share-offsite/?url=" + HttpUtility.UrlEncode(Eval("UrlPartage").ToString()) %>'>
                                                    <i class="bi bi-linkedin"></i>
                                                </a>

                                                <%-- Twitter --%>
                                                <a class="btn btn-sm btn-light" target="_blank"
                                                    title="Partager sur Twitter"
                                                    href='<%# "https://twitter.com/intent/tweet?text=" + HttpUtility.UrlEncode("Découvrez cet article : " + Eval("UrlPartage")) %>'>
                                                    <i class="bi bi-twitter"></i>
                                                </a>

                                                <%-- Instagram (lien vers profil ou compte, car le partage direct n'est pas possible) --%>
                                                <a class="btn btn-sm btn-light" target="_blank"
                                                    title="Voir sur Instagram"
                                                    href="https://www.instagram.com/">
                                                    <i class="bi bi-instagram"></i>
                                                </a>

                                                <%-- Copier le lien --%>
                                                <button class="btn btn-sm btn-light" type="button"
                                                    title="Copier le lien"
                                                    onclick='copyToClipboard("<%# Eval("UrlPartage") %>")'>
                                                    <i class="bi bi-link-45deg"></i>
                                                </button>

                                                <!-- Copier le lien -->
                                                <button class="btn btn-light btn-sm" onclick='copyToClipboard("<%# Eval("UrlPartage") %>")'>
                                                    <i class="bi bi-clipboard"></i>Copier le lien
                                                </button>



                                            </div>

                                        </div>
                                    </div>
                                </ItemTemplate>
                            </asp:ListView>
                            <!-- Fin bloc activité -->


                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="row gy-4">
                            <div class="widgets-container">
                                <div class="search-sidebar p-3 bg-light rounded shadow-sm">
                                    <h5>Filtrer les articles</h5>

                                    <!--/Categories Widget -->
                                    <div id="blogFilterForm">
                                        <div class="mb-3">
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Recherche par nom de l'organisation, ou de l'auteur du post, ou de la date du post</label>
                                            <input type="text" runat="server" id="txtsearch" class="form-control" placeholder="">
                                        </div>
                                        <button type="submit" class="btn btn-success w-100" runat="server" id="btnsearch" onserverclick="btnsearch_ServerClick">Rechercher</button>
                                    </div>
                                </div>
                                <!-- Categories Widget -->
                                <div class="categories-widget widget-item">

                                    <h3 class="widget-title">Domaines d'Intervention</h3>
                                    <ul class="mt-3">
                                        <asp:ListView ID="listcategorie" runat="server" OnItemCommand="listcategorie_ItemCommand">
                                            <EmptyItemTemplate>Aucune Donnée</EmptyItemTemplate>
                                            <ItemTemplate>
                                                <li>
                                                    <asp:LinkButton runat="server" CommandName="viewdom" CommandArgument='<%# Eval("Id") %>'>
                                                        <%#  HttpUtility.HtmlEncode(Eval("Libelle")) %>
                                                        <span>(<b><%#  HttpUtility.HtmlEncode(Eval("NombrePosts")) %> </b>)</span>

                                                    </asp:LinkButton>

                                                </li>

                                            </ItemTemplate>
                                        </asp:ListView>

                                    </ul>

                                </div>
                                <!--/Categories Widget -->

                                <!-- Publicité - Carrousel -->
                                <div class="bg-white shadow-sm p-4 rounded-4">
                                    <h5 class="fw-bold mb-3 text-center">Publicité</h5>
                                    <div id="carouselPub" class="carousel slide" data-bs-ride="carousel">
                                        <div class="carousel-inner rounded">
                                            <div class="carousel-item active">
                                                <img src="assets/img/blog/skills.png" class="d-block w-100" alt="Ad 1">
                                            </div>
                                            <div class="carousel-item">
                                                <img src="assets/img/blog/skills.png" class="d-block w-100" alt="Ad 2">
                                            </div>
                                            <div class="carousel-item">
                                                <img src="assets/img/blog/skills.png" class="d-block w-100" alt="Ad 3">
                                            </div>
                                        </div>
                                        <button class="carousel-control-prev" type="button" data-bs-target="#carouselPub" data-bs-slide="prev">
                                            <span class="carousel-control-prev-icon bg-dark rounded-circle" aria-hidden="true"></span>
                                            <span class="visually-hidden">Précédent</span>
                                        </button>
                                        <button class="carousel-control-next" type="button" data-bs-target="#carouselPub" data-bs-slide="next">
                                            <span class="carousel-control-next-icon bg-dark rounded-circle" aria-hidden="true"></span>
                                            <span class="visually-hidden">Suivant</span>
                                        </button>
                                    </div>
                                </div>


                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Pagination du Blog -->
        <section id="blog-pagination" class="blog-pagination section">
            <div class="container">
                <div class="d-flex justify-content-center">
                    <ul>
                        <li><a href="#"><i class="bi bi-chevron-left"></i></a></li>
                        <li><a href="#">1</a></li>
                        <li><a href="#" class="active">2</a></li>
                        <li><a href="#">3</a></li>
                        <li><a href="#">4</a></li>
                        <li>...</li>
                        <li><a href="#">10</a></li>
                        <li><a href="#"><i class="bi bi-chevron-right"></i></a></li>
                    </ul>
                </div>
            </div>
        </section>
        <!-- Fin Pagination du Blog -->

    </main>
    <style>
        .activity-card {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 12px;
            box-shadow: 0 1px 4px rgba(0,0,0,0.04);
            font-size: 0.9rem;
        }

        .activity-header .org-logo {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .activity-info .org-name {
            font-size: 1rem;
            font-weight: 500;
            margin: 0;
        }

        .activity-title {
            font-weight: 600;
            color: #0a66c2;
            text-decoration: none;
        }

            .activity-title:hover {
                text-decoration: underline;
            }

        .activity-snippet {
            color: #444;
            font-size: 0.88rem;
            margin-top: 5px;
        }

        .activity-image {
            width: 100%;
            max-height: 180px;
            object-fit: cover;
            border-radius: 6px;
            margin-top: 10px;
        }

        .activity-footer {
            display: flex;
            gap: 8px;
            margin-top: 10px;
        }

            .activity-footer .btn {
                font-size: 0.8rem;
                padding: 4px 10px;
                border: 1px solid #ddd;
            }

        .search-sidebar {
            background: #f9f9f9;
        }

            .search-sidebar h5 {
                margin-bottom: 15px;
            }

        .activity-card {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 12px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        }

        .search-sidebar {
            max-height: 80vh;
            overflow-y: auto;
        }
    </style>
    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function () {
                // Message simple
                alert("✅ Le lien a été copié dans le presse-papiers !");
            }).catch(function (err) {
                alert("❌ Erreur lors de la copie : " + err);
            });
        }
    </script>


</asp:Content>
