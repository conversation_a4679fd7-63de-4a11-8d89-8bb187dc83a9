﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="programme.aspx.cs" Inherits="LinCom.programme" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Page Title -->
    <div class="page-title">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h2>Programmes & Initiatives de FNUAP</h2>
                        <p class="mb-0">Odio et unde deleniti. Deserunt numquam exercitationem. Officiis quo odio sint voluptas consequatur ut a odio voluptatem. Sit dolorum debitis veritatis natus dolores. Quasi ratione sint. Sit quaerat ipsum dolorem.</p>
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="home.aspx">Home</a></li>
                    <li><a href="#">ONG</a></li>
                    <li class="current"><a href="ong.aspx">Liste des Organisations</a></li>
                </ol>
            </div>
        </nav>
    </div>
    <!-- End Page Title -->
    <main class="main">

        <!-- Filter Section -->
        <section class="filter-section py-4 bg-light">
            <div class="container d-flex flex-wrap justify-content-center gap-3">
                <select id="categoryFilter" class="form-select form-select-md" style="max-width: 220px;">
                    <option value="">Toutes les catégories</option>
                    <option value="Education">Éducation</option>
                    <option value="Environment">Environnement</option>
                    <option value="Health">Santé</option>
                </select>
                <input type="text" runat="server" id="txtsearch" class="form-control form-control-md" style="max-width: 220px;" placeholder="Date de lancement">
                <button type="submit" class="btn btn-success btn-md px-4">Filtrer</button>
            </div>
        </section>

        <!-- Programs Grid -->
        <section id="programs" class="programs-section py-5">
            <div class="container">
                <div class="row g-4 justify-content-center">

                    <!-- Program Card -->
                    <asp:ListView ID="listpost" runat="server" OnItemCommand="listpost_ItemCommand" OnItemDataBound="listinst_ItemDataBound">
                        <EmptyItemTemplate>Aucune Donnée</EmptyItemTemplate>
                        <ItemTemplate>
                            <div class="col-sm-12 col-md-6 col-lg-4 mb-4 d-flex">
                                <article class="card shadow-sm h-100 border-0 rounded-4 overflow-hidden w-100">
                                    <!-- Image fixe 220px -->
                                    <img src='<%# HttpUtility.HtmlEncode(string.Concat("../file/program/", Eval("photo"))) %>'
                                        alt="Illustration de la publication"
                                        class="card-img-top"
                                        style="height: 220px; object-fit: cover;">

                                    <div class="card-body d-flex flex-column">
                                        <%-- Domaines d'intervention --%>
                                        <div class="mb-2">
                                            <asp:Repeater ID="rptDomaines" runat="server">
                                                <ItemTemplate>
                                                    <span class="badge bg-success me-1 mb-1"><%# Eval("libelle") %></span>
                                                </ItemTemplate>
                                            </asp:Repeater>
                                        </div>

                                        <%-- Titre de la publication --%>
                                        <h5 class="card-title mb-2">

                                            <asp:LinkButton runat="server" class="stretched-link text-decoration-none text-dark fw-bold" CommandName="viewblog" CommandArgument='<%# Eval("name") %>'>
      <%# HttpUtility.HtmlEncode(Eval("Titre")) %>

                                            </asp:LinkButton>
                                            <a href="#"></a>
                                        </h5>

                                        <%-- Organisation & Date --%>
                                        <div class="mt-auto d-flex align-items-center pt-3 border-top">
                                            <img src='<%# HttpUtility.HtmlEncode(string.Concat("../file/organ/", Eval("Photoorg"))) %>'
                                                alt="Logo de l'organisation"
                                                class="rounded-circle me-3"
                                                width="48"
                                                height="48"
                                                style="object-fit: cover;">
                                            <div>
                                                <p class="mb-0 fw-semibold"><%# Eval("Nomorg") %></p>
                                                <small class="text-muted">
                                                    <%# GetRelativeDate(Convert.ToDateTime(Eval("DatePublication"))) %>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </article>
                            </div>
                        </ItemTemplate>
                    </asp:ListView>

                </div>
            </div>
        </section>

        <!-- Pagination -->
        <section class="pagination-section py-4">
            <div class="container d-flex justify-content-center">
                <nav aria-label="Page navigation">
                    <ul class="pagination pagination-rounded gap-2 mb-0">
                        <li class="page-item">
                            <a class="page-link" href="#" aria-label="Previous"><i class="bi bi-chevron-left"></i></a>
                        </li>
                        <li class="page-item"><a class="page-link" href="#">1</a></li>
                        <li class="page-item active"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item"><a class="page-link" href="#">4</a></li>
                        <li class="page-item disabled"><span class="page-link">...</span></li>
                        <li class="page-item"><a class="page-link" href="#">10</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#" aria-label="Next"><i class="bi bi-chevron-right"></i></a>
                        </li>
                    </ul>
                </nav>
            </div>
        </section>

    </main>

    <style>
        /* Reset some basics */
        .main a {
            transition: color 0.3s ease;
        }

            .main a:hover {
                color: #28a745 !important;
            }

        .page-title h1 {
            font-weight: 700;
            font-size: 2.8rem;
        }

        .filter-section .form-select,
        .filter-section .form-control {
            border-radius: 50px;
            box-shadow: 0 2px 6px rgb(0 0 0 / 0.1);
        }

        .filter-section .btn {
            border-radius: 50px;
            font-weight: 600;
        }

        /* Cards */
        .card {
            box-shadow: 0 6px 20px rgb(0 0 0 / 0.1);
        }

            .card:hover {
                box-shadow: 0 10px 30px rgb(0 0 0 / 0.15);
                transform: translateY(-6px);
            }

            .card .badge {
                font-size: 0.8rem;
                letter-spacing: 0.05em;
                text-transform: uppercase;
            }

        .card-title a {
            font-size: 1.25rem;
        }

        .pagination .page-link {
            border-radius: 50% !important;
            width: 38px;
            height: 38px;
            text-align: center;
            line-height: 38px;
        }

        .pagination .page-item.active .page-link {
            background-color: #28a745;
            border-color: #28a745;
            color: white;
        }
    </style>

</asp:Content>
