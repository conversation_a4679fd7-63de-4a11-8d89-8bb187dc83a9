﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IMentor
    {
        void AfficherDetails(int mentorId, Mentor_Class mentor);
        int Ajouter(Mentor_Class mentor);
        int Modifier(Mentor_Class mentor);
        int Supprimer(int mentorId);
        void ChargerMentors(GridView gdv, string status = "");
        void ChargerMentorsParProgramme(GridView gdv, int programmeMentoratId);
        void chargerMentors(DropDownList lst);
        void ChargerMentorsParMembre(GridView gdv, long membreId);
       
    }
}
