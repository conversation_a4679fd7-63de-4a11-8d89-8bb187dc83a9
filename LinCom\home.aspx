﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="home.aspx.cs" Inherits="LinCom.home" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Hero Section -->
    <section id="hero" class="hero section accent-background">
        <div class="container position-relative" data-aos="fade-up" data-aos-delay="100">
            <div class="row gy-5 justify-content-between">
                <div class="col-lg-6 order-2 order-lg-1 d-flex flex-column justify-content-center">
                    <h3>
                        <span>Ecosystème Numérique  </span>
                        <span class="accent">des Acteurs d'Impact Social et Communautaire.</span></h3>
                    <p>
                        Platerforme permettant aux organisations des jeunes d’impact social à interagir avec efficacité et
 efficience pour un développement socio économique inclusif.
                    </p>
                    <div class="d-flex">
                        <a href="inscriptionorganisation.aspx" class="btn-get-started">Inscrire Une Organisation</a>
                        <a href="https://www.youtube.com/watch?v=Y7f98aduVJ8" class="glightbox btn-watch-video d-flex align-items-center"><i class="bi bi-play-circle"></i><span>Vidéo LinCom</span></a>
                    </div>
                </div>
                <div class="col-lg-5 order-1 order-lg-2">
                    <img src="assets/img/hero-img.svg" class="img-fluid" alt="">
                </div>
            </div>
        </div>

        <div class="icon-boxes position-relative" data-aos="fade-up" data-aos-delay="200">
            <div class="container position-relative">
                <div class="row gy-4 mt-5">

                    <div class="col-xl-3 col-md-4">
                        <div class="icon-box">
                            <div class="icon"><i class="bi bi-easel"></i></div>
                            <h4 class="title"><a href="" class="stretched-link">Espace cooperateur des membres des organisations des jeunes  d'impact socio-économique</a></h4>
                        </div>
                    </div>
                    <!--End Icon Box -->

                    <div class="col-xl-3 col-md-4">
                        <div class="icon-box">
                            <div class="icon"><i class="bi bi-gem"></i></div>
                            <h4 class="title"><a href="" class="stretched-link">Espace de collaboration, complémentarité, synergie, communication,
 coordination et la mise en réseau des organisations des jeunes  d'impact socio-économique</a></h4>
                        </div>
                    </div>
                    <!--End Icon Box -->

                    <div class="col-xl-3 col-md-4">
                        <div class="icon-box">
                            <div class="icon"><i class="bi bi-geo-alt"></i></div>
                            <h4 class="title"><a href="" class="stretched-link">Espace de  gestion administrative et financière des organisations
 des organisations de jeunes d'impact socio-économique</a></h4>
                        </div>
                    </div>
                    <!--End Icon Box -->

                    <%--  <div class="col-xl-3 col-md-4">
                        <div class="icon-box">
                            <div class="icon"><i class="bi bi-command"></i></div>
                            <h4 class="title"><a href="" class="stretched-link">Espace de gestion des projets et initiatives des organisations de jeunes d'impact socio-économique</a></h4>
                        </div>
                    </div>--%>
                    <!--End Icon Box -->

                </div>
            </div>
        </div>

    </section>
    <!-- /Hero Section -->


    <!-- Stats Section -->
    <section id="stats" class="stats section">

        <div class="container" data-aos="fade-up" data-aos-delay="100">


            <div class="row gy-4 align-items-center">

                <div class="col-lg-5">
                    <img src="assets/img/stats-img.svg" alt="" class="img-fluid">
                </div>

                <div class="col-lg-7">

                    <div class="row gy-4">

                        <div class="col-lg-6">
                            <div class="stats-item d-flex">
                                <i class="bi bi-emoji-smile flex-shrink-0"></i>
                                <div>
                                    <span runat="server" id="nbreorg" data-purecounter-start="0" data-purecounter-end="232" data-purecounter-duration="1" class="purecounter"></span>
                                    <p><strong>Organisations</strong> <span>de jeunes d'impact socio-économique</span></p>
                                </div>
                            </div>
                        </div>
                        <!-- End Stats Item -->

                        <div class="col-lg-6">
                            <div class="stats-item d-flex">
                                <i class="bi bi-journal-richtext flex-shrink-0"></i>
                                <div>
                                    <span runat="server" id="nbreprojet" data-purecounter-start="0" data-purecounter-end="521" data-purecounter-duration="1" class="purecounter"></span>
                                    <p><strong>Projects</strong> <span>publiés</span></p>
                                </div>
                            </div>
                        </div>
                        <!-- End Stats Item -->

                        <div class="col-lg-6">
                            <div class="stats-item d-flex">
                                <i class="bi bi-headset flex-shrink-0"></i>
                                <div>
                                    <span runat="server" id="nbrememb" data-purecounter-start="0" data-purecounter-end="1453" data-purecounter-duration="1" class="purecounter"></span>
                                    <p><strong>Jeunes Connectés</strong> <span>des organisations de jeunes d'impact socio-économique</span></p>
                                </div>
                            </div>
                        </div>
                        <!-- End Stats Item -->

                        <div class="col-lg-6">
                            <div class="stats-item d-flex">
                                <i class="bi bi-people flex-shrink-0"></i>
                                <div>
                                    <span runat="server" id="nbreparte" data-purecounter-start="0" data-purecounter-end="32" data-purecounter-duration="1" class="purecounter"></span>
                                    <p><strong>Partenaires</strong> <span></span></p>
                                </div>
                            </div>
                        </div>
                        <!-- End Stats Item -->

                    </div>

                </div>

            </div>

        </div>

    </section>
    <!-- /Stats Section -->

  <!-- StatsOrg Section -->
<section class="py-5" style="background-color: #008374;">
    <div class="container text-white">
        <!-- Titre principal -->
        <div class="text-center mb-5">
            <h2 class="fw-bold">Répartition des organisations par province</h2>
            <p class="fs-5">Nombre total : <asp:Label ID="lblTotalOrgs" runat="server" CssClass="fw-bold"></asp:Label> organisations</p>
        </div>

        <!-- Grille des provinces -->
        <div class="row g-4">
            <asp:ListView ID="listinst" runat="server">
                <ItemTemplate>
                    <div class="col-md-6 col-lg-4">
                        <div class="card text-dark shadow-sm h-100">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="bi bi-geo-fill me-2 text-primary"></i>
                                    <%# Eval("code") %>
                                </h5>
                                <p class="card-text fs-5">
                                    <span class="badge bg-success p-2"> <%# Eval("number") %> organisations</span>
                                </p>
                                <div class="progress mt-3" style="height: 8px;">
                                    <div class="progress-bar bg-primary" role="progressbar" style='<%# "width:" + Eval("number") + "%;" %>'></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </ItemTemplate>
                <EmptyDataTemplate>
                    <div class="col-12 text-center">
                        <p class="text-warning">Aucune donnée disponible</p>
                    </div>
                </EmptyDataTemplate>
            </asp:ListView>
        </div>
    </div>
</section>

    <!-- Call To Action Section -->
    <section id="call-to-action" class="call-to-action section dark-background">

        <div class="container">
            <img src="assets/img/cta-bg.jpg" alt="">
            <div class="content row justify-content-center" data-aos="zoom-in" data-aos-delay="100">
                <div class="col-xl-10">
                    <div class="text-center">

                        <h3>Cartographie des organisations des jeunes d'impact</h3>
                        <%--  <iframe src="https://www.google.com/maps/d/embed?mid=12e0lIXr_bQbBISMg-Iv9dkWQR-sb5FI&ehbc=2E312F" frameborder="0" style="border: 0; width: 100%; height: 500px;" allowfullscreen></iframe>
                        --%>
                        <iframe src="https://www.google.com/maps/d/embed?mid=12e0lIXr_bQbBISMg-Iv9dkWQR-sb5FI&ehbc=2E312F&noprof=1" frameborder="0" style="border: 0; width: 100%; height: 500px;" allowfullscreen></iframe>

                        <%--    <iframe src="https://www.google.com/maps/d/edit?mid=12e0lIXr_bQbBISMg-Iv9dkWQR-sb5FI&usp=sharing" frameborder="0" style="border: 0; width: 100%; height: 500px;" allowfullscreen></iframe>--%>
                        <%-- <iframe src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d12097.433213460943!2d-74.0062269!3d40.7101282!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0xb89d1fe6bc499443!2sDowntown+Conference+Center!5e0!3m2!1smk!2sbg!4v1539943755621" frameborder="0" style="border: 0; width: 100%; height: 500px;" allowfullscreen></iframe>
                        --%>
                    </div>
                </div>
            </div>
        </div>

    </section>
    <!-- /Call To Action Section -->

    <!-- Recent Posts Section -->
    <section id="recent-posts" class="recent-posts section">

        <!-- Section Title -->
        <div class="container section-title" data-aos="fade-up">
            <h2>Dernières Publications</h2>
            <p>Necessitatibus eius consequatur ex aliquid fuga eum quidem sint consectetur velit</p>
        </div>
        <!-- End Section Title -->

        <div class="container">

            <div class="row gy-4">
                <asp:ListView ID="listpost" runat="server" OnItemCommand="listpost_ItemCommand">
                    <EmptyItemTemplate>Aucune Donnée</EmptyItemTemplate>
                    <ItemTemplate>
                        <div class="col-xl-4 col-md-4" data-aos="fade-up" data-aos-delay="100">

                            <article>
                                <asp:HiddenField ID="hfPostId" runat="server" Value='<%# HttpUtility.HtmlEncode(Eval("name")) %>' />
                                <div class="post-img">
                                    <img src='<%# HttpUtility.HtmlEncode( string.Concat("../file/actualit/",Eval("photo"))) %>' alt="" class="img-fluid">
                                </div>

                                <p class="post-category">
                                    <asp:ListView ID="listdomain" runat="server">
                                        <EmptyItemTemplate>Aucune Donnée</EmptyItemTemplate>
                                        <ItemTemplate>
                                            <span><%#  HttpUtility.HtmlEncode(Eval("PostTitre")) %></span>
                                        </ItemTemplate>
                                    </asp:ListView>

                                </p>

                                <h2 class="title">
                                     <asp:LinkButton runat="server" CommandName="view" CommandArgument='<%# Eval("name") %>'><%# HttpUtility.HtmlEncode( Eval("Titre")) %><i class="fa fa-arrow-right"></i></asp:LinkButton>
                       
                                </h2>

                                <div class="d-flex align-items-center">
                                    <img src='<%# HttpUtility.HtmlEncode(string.Concat("../file/actualit/",Eval("photo"))) %>' alt="" class="post-author-img flex-shrink-0">
                                    <div class="post-meta">
                                        <p class="post-author">Publié par <%# HttpUtility.HtmlEncode( Eval("Nomorg")) %></p>
                                        <p class="post-date">
                                            <time datetime="2022-01-01">Publié le <%# HttpUtility.HtmlEncode( Eval("DatePublication")) %></time>
                                        </p>
                                    </div>
                                </div>

                            </article>
                        </div>

                    </ItemTemplate>
                </asp:ListView>
                <!-- End post list item -->
            </div>
        </div>

    </section>
    <!-- /Recent Posts Section -->

  

    <!-- Portfolio Section -->
    <section id="portfolio" class="portfolio section">

         <!-- Section Title -->
  <div class="container section-title" data-aos="fade-up">
      <h2>Événements à venir</h2>
      <p>Participez à nos prochaines initiatives, conférences et ateliers pour enrichir vos compétences et renforcer votre engagement.</p>
  </div>
  <!-- End Section Title -->

        <div class="container">

            <div class="isotope-layout" data-default-filter="*" data-layout="masonry" data-sort="original-order">

                <div class="row gy-4 isotope-container" data-aos="fade-up" data-aos-delay="200">
                    <!-- Liste dynamique via ASP.NET -->
                    <asp:ListView ID="listevent" runat="server" OnItemCommand="listevent_ItemCommand">
                        <EmptyItemTemplate>Aucune donnée pour le moment.</EmptyItemTemplate>
                        <ItemTemplate>
                            <div class="col-lg-4 col-md-6 portfolio-item isotope-item filter-app">
                                <div class="portfolio-content h-100">
                                    <a href='<%# HttpUtility.HtmlEncode(string.Concat("../file/post/",Eval("photo"))) %>' data-gallery="portfolio-gallery-app" class="glightbox">
                                        <img src='<%# HttpUtility.HtmlEncode(string.Concat("../file/post/",Eval("photo"))) %>' class="img-fluid fixed-img" alt=""></a>
                                    <div class="portfolio-info">

                                        <h4>
                                              <asp:LinkButton runat="server" CommandName="viewevent" CommandArgument='<%# Eval("name") %>'><%# HttpUtility.HtmlEncode( Eval("Titre")) %><i class="fa fa-arrow-right"></i></asp:LinkButton>
                       

                                        </h4>
                                        <p class="text-muted mb-1">
                                            <i class="bi bi-clock"></i><b><%# Eval("DatePublication") %></b> | <i class="bi bi-geo-alt"></i><b><%# Eval("eventplace") %> </b>
                                        </p>
                                        <p><%# Eval("summery") %></p>
                                         <asp:LinkButton class="readmore stretched-link" runat="server" CommandName="viewevent1" CommandArgument='<%# Eval("name") %>'>Lire Plus<i class="bi bi-arrow-right"></i></asp:LinkButton>
                       

                                    </div>
                                </div>
                            </div>
                            <!-- End Portfolio Item -->
                        </ItemTemplate>
                    </asp:ListView>


                </div>
                <!-- End Portfolio Container -->

            </div>

        </div>

    </section>
    <!-- /Portfolio Section -->
    <!-- Section Témoignages -->
    <section id="testimonials" class="testimonials section">

        <!-- Titre de la section -->
        <div class="container section-title" data-aos="fade-up">
            <h2>Témoignages des membres des organisations de jeunes à impact socio-économique</h2>
            <p>Découvrez ce que disent nos partenaires engagés pour le changement social et économique.</p>
        </div>
        <!-- Fin du titre -->

        <div class="container" data-aos="fade-up" data-aos-delay="100">

            <div class="swiper init-swiper">
                <script type="application/json" class="swiper-config">
        {
          "loop": true,
          "speed": 600,
          "autoplay": {
            "delay": 5000
          },
          "slidesPerView": "auto",
          "pagination": {
            "el": ".swiper-pagination",
            "type": "bullets",
            "clickable": true
          },
          "breakpoints": {
            "320": {
              "slidesPerView": 1,
              "spaceBetween": 40
            },
            "1200": {
              "slidesPerView": 3,
              "spaceBetween": 10
            }
          }
        }
                </script>

                <div class="swiper-wrapper">

                    <!-- Témoignage 1 -->
                    <div class="swiper-slide">
                        <div class="testimonial-item">
                            <img src="assets/img/testimonials/skills.png" class="testimonial-img" alt="">
                            <h3>Saul Goodman</h3>
                            <h4>CEO & Fondateur</h4>
                            <div class="stars">
                                <i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i>
                            </div>
                            <p>
                                <i class="bi bi-quote quote-icon-left"></i>
                                <span>Grâce à cette initiative, nous avons renforcé notre impact communautaire et développé des compétences essentielles pour l’avenir.</span>
                                <i class="bi bi-quote quote-icon-right"></i>
                            </p>
                        </div>
                    </div>

                    <!-- Témoignage 2 -->
                    <div class="swiper-slide">
                        <div class="testimonial-item">
                            <img src="assets/img/testimonials/skills.png" class="testimonial-img" alt="">
                            <h3>Sara Wilsson</h3>
                            <h4>Designer</h4>
                            <div class="stars">
                                <i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i>
                            </div>
                            <p>
                                <i class="bi bi-quote quote-icon-left"></i>
                                <span>Une plateforme exceptionnelle qui connecte les jeunes à des opportunités concrètes et valorise leur engagement.</span>
                                <i class="bi bi-quote quote-icon-right"></i>
                            </p>
                        </div>
                    </div>

                    <!-- Témoignage 3 -->
                    <div class="swiper-slide">
                        <div class="testimonial-item">
                            <img src="assets/img/testimonials/skills.png" class="testimonial-img" alt="">
                            <h3>Jena Karlis</h3>
                            <h4>Commerçante</h4>
                            <div class="stars">
                                <i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i>
                            </div>
                            <p>
                                <i class="bi bi-quote quote-icon-left"></i>
                                <span>J’ai pu accéder à un réseau solide de partenaires et bénéficier d’un accompagnement sur mesure.</span>
                                <i class="bi bi-quote quote-icon-right"></i>
                            </p>
                        </div>
                    </div>

                    <!-- Témoignage 4 -->
                    <div class="swiper-slide">
                        <div class="testimonial-item">
                            <img src="assets/img/testimonials/skills.png" class="testimonial-img" alt="">
                            <h3>Matt Brandon</h3>
                            <h4>Freelance</h4>
                            <div class="stars">
                                <i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i>
                            </div>
                            <p>
                                <i class="bi bi-quote quote-icon-left"></i>
                                <span>Un vrai tremplin pour les jeunes talents souhaitant s’impliquer et bâtir des projets d’impact durable.</span>
                                <i class="bi bi-quote quote-icon-right"></i>
                            </p>
                        </div>
                    </div>

                    <!-- Témoignage 5 -->
                    <div class="swiper-slide">
                        <div class="testimonial-item">
                            <img src="assets/img/testimonials/skills.png" class="testimonial-img" alt="">
                            <h3>John Larson</h3>
                            <h4>Entrepreneur</h4>
                            <div class="stars">
                                <i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i>
                            </div>
                            <p>
                                <i class="bi bi-quote quote-icon-left"></i>
                                <span>Une initiative inspirante et bien structurée qui permet aux jeunes de devenir acteurs de changement dans leurs communautés.</span>
                                <i class="bi bi-quote quote-icon-right"></i>
                            </p>
                        </div>
                    </div>

                </div>

                <!-- Pagination -->
                <div class="swiper-pagination"></div>
            </div>

        </div>

    </section>
    <!-- /Section Témoignages -->

    <!-- Clients Section -->
    <section id="clients" class="clients section">

        <div class="container">

            <div class="swiper init-swiper">
                <script type="application/json" class="swiper-config">
            {
              "loop": true,
              "speed": 600,
              "autoplay": {
                "delay": 5000
              },
              "slidesPerView": "auto",
              "pagination": {
                "el": ".swiper-pagination",
                "type": "bullets",
                "clickable": true
              },
              "breakpoints": {
                "320": {
                  "slidesPerView": 2,
                  "spaceBetween": 40
                },
                "480": {
                  "slidesPerView": 3,
                  "spaceBetween": 60
                },
                "640": {
                  "slidesPerView": 4,
                  "spaceBetween": 80
                },
                "992": {
                  "slidesPerView": 6,
                  "spaceBetween": 120
                }
              }
            }
                </script>
                <div class="swiper-wrapper align-items-center">
                      <asp:ListView ID="listpartenaire" runat="server">
      <EmptyItemTemplate>Aucune donnée pour le moment.</EmptyItemTemplate>
      <ItemTemplate>
                    <div class="swiper-slide">
                        <img src='<%# HttpUtility.HtmlEncode(string.Concat("../file/partner/",Eval("logo"))) %>'  class="img-fluid" alt='<%# Eval("Nom") %>'>
                    </div>
          </ItemTemplate>
                          </asp:ListView>
                   
                </div>
            </div>

        </div>

    </section>
    <!-- /Clients Section -->
    <style>
        .fixed-img {
            width: 100%;
            height: 200px; /* ou une hauteur de ton choix */
            object-fit: cover;
            object-position: center;
            display: block;
            border-radius: 8px; /* optionnel */
        }
        .progress {
  background-color: #f1f1f1;
  border-radius: 20px;
  overflow: hidden;
}

.progress-bar {
  font-size: 0.9rem;
  font-weight: 600;
  line-height: 1.5;
  border-radius: 20px;
}

    </style>
</asp:Content>
