﻿using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class MembresOrganisationImp : IMembresOrganisation
    {
        private MembresOrganisation membresOrganisation = new MembresOrganisation();
        int msg;

        public void AfficherDetails(long membresOrganisationId, MembresOrganisation_Class membresOrganisationClass, int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var mo = con.MembresOrganisations.FirstOrDefault(x => x.MembresOrganisationId == membresOrganisationId);
                    if (mo != null)
                    {
                        membresOrganisationClass.MembresOrganisationId = mo.MembresOrganisationId;
                        membresOrganisationClass.MembreId = mo.MembreId;
                        membresOrganisationClass.OrganisationId = mo.OrganisationId;
                        membresOrganisationClass.Poste = mo.Poste;
                        membresOrganisationClass.DateAdhesion = mo.DateAdhesion;
                        membresOrganisationClass.Name = mo.name;
                        membresOrganisationClass.Statut = mo.statut;
                        membresOrganisationClass.RoleMembreID = mo.RoleMembreID;
                    }
                }
                else if (cd==1)
                {
                    var mo = con.MembresOrganisations.Distinct().FirstOrDefault(x => x.MembreId == membresOrganisationId);
                    if (mo != null)
                    {
                        membresOrganisationClass.MembresOrganisationId = mo.MembresOrganisationId;
                        membresOrganisationClass.MembreId = mo.MembreId;
                        membresOrganisationClass.OrganisationId = mo.OrganisationId;
                        membresOrganisationClass.Poste = mo.Poste;
                        membresOrganisationClass.DateAdhesion = mo.DateAdhesion;
                        membresOrganisationClass.Name = mo.name;
                        membresOrganisationClass.Statut = mo.statut;
                        membresOrganisationClass.RoleMembreID = mo.RoleMembreID;
                    }
                }
                  
            }
        }
        public void AfficherDetails(string name, MembresOrganisation_Class membresOrganisationClass)
        {
            using (Connection con = new Connection())
            {
                var mo = con.MembresOrganisations.FirstOrDefault(x => x.name == name);
                if (mo != null)
                {
                    membresOrganisationClass.MembresOrganisationId = mo.MembresOrganisationId;
                    membresOrganisationClass.MembreId = mo.MembreId;
                    membresOrganisationClass.OrganisationId = mo.OrganisationId;
                    membresOrganisationClass.Poste = mo.Poste;
                    membresOrganisationClass.DateAdhesion = mo.DateAdhesion;
                    membresOrganisationClass.Name = mo.name;
                    membresOrganisationClass.Statut = mo.statut;
                    membresOrganisationClass.RoleMembreID = mo.RoleMembreID;
                }
            }
        }

        public int Ajouter(MembresOrganisation_Class membresOrganisationClass)
        {
            using (Connection con = new Connection())
            {
               
                // Ajouter un nouveau membre à l'organisation
                membresOrganisation.MembreId = membresOrganisationClass.MembreId;
                membresOrganisation.OrganisationId = membresOrganisationClass.OrganisationId;
                membresOrganisation.Poste = membresOrganisationClass.Poste;
                membresOrganisation.DateAdhesion = membresOrganisationClass.DateAdhesion ?? DateTime.Now;
                membresOrganisation.name = membresOrganisationClass.Name;
                membresOrganisation.statut = membresOrganisationClass.Statut;
                membresOrganisation.RoleMembreID = membresOrganisationClass.RoleMembreID;

                try
                {
                    con.MembresOrganisations.Add(membresOrganisation);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }

        public void ChargerMembresGridview(GridView gdv,string code,int cd,string statut, long organisationId)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var query = from mo in con.MembresOrganisations
                                join m in con.Membres on mo.MembreId equals m.MembreId
                                join o in con.Organisations on mo.OrganisationId equals o.OrganisationId
                                where mo.OrganisationId == organisationId && mo.statut == statut && mo.RoleMembreID != -1
                                select new
                                {
                                    id=mo.MembresOrganisationId,
                                    idmembe=mo.MembreId,
                                    NomComplet = m.Nom + " " + m.Prenom,
                                    email=m.Email,
                                    phone=m.Telephone,
                                    poste=mo.Poste,
                                    DateAdhesion=mo.DateAdhesion,
                                    Statut = mo.statut,
                                    Type = mo.name,
                                    Titreorg=o.Nom,
                                    photo=m.PhotoProfil,
                                };

                    gdv.DataSource = query.ToList();
                    gdv.DataBind();
                }
                else if (cd==1)
                {
                    var query = from mo in con.MembresOrganisations
                                join m in con.Membres on mo.MembreId equals m.MembreId
                                join o in con.Organisations on mo.OrganisationId equals o.OrganisationId
                                where mo.OrganisationId == organisationId
                                select new
                                {
                                    id = mo.MembresOrganisationId,
                                    idmembe = mo.MembreId,
                                    NomComplet = m.Nom + " " + m.Prenom,
                                    email = m.Email,
                                    phone = m.Telephone,
                                    poste = mo.Poste,
                                    DateAdhesion = mo.DateAdhesion,
                                    Statut = mo.statut,
                                    Type = mo.name,
                                    photo = m.PhotoProfil,
                                    Titreorg = o.Nom
                                };

                    gdv.DataSource = query.ToList();
                    gdv.DataBind();
                }
                  
            }
        }
        public void ChargerMembresListview(ListView gdv, string code, int cd, string statut, long organisationId)
        {
            using (Connection con = new Connection())
            {
                if (cd == 0)
                {
                    var query = from mo in con.MembresOrganisations
                                join m in con.Membres on mo.MembreId equals m.MembreId
                                join o in con.Organisations on mo.OrganisationId equals o.OrganisationId
                                where mo.OrganisationId == organisationId && mo.statut == statut && mo.RoleMembreID!=-1
                                select new
                                {
                                    id = mo.MembresOrganisationId,
                                    idmembe = mo.MembreId,
                                    NomComplet = m.Nom + " " + m.Prenom,
                                    email = m.Email,
                                    phone = m.Telephone,
                                    poste = mo.Poste,
                                    DateAdhesion = mo.DateAdhesion,
                                    Statut = mo.statut,
                                    Type = mo.name,
                                    photo = m.PhotoProfil,
                                    Titreorg = o.Nom
                                };

                    gdv.DataSource = query.ToList();
                    gdv.DataBind();
                }
                else if (cd == 1)
                {
                    var query = from mo in con.MembresOrganisations
                                join m in con.Membres on mo.MembreId equals m.MembreId
                                join o in con.Organisations on mo.OrganisationId equals o.OrganisationId
                                where mo.OrganisationId == organisationId && mo.statut == statut && mo.RoleMembreID != -1
                                select new
                                {
                                    id = mo.MembresOrganisationId,
                                    idmembe = mo.MembreId,
                                    NomComplet = m.Nom + " " + m.Prenom,
                                    email = m.Email,
                                    phone = m.Telephone,
                                    poste = mo.Poste,
                                    DateAdhesion = mo.DateAdhesion,
                                    Statut = mo.statut,
                                    Type = mo.name,
                                    photo = m.PhotoProfil,
                                    Titreorg = o.Nom
                                };

                    gdv.DataSource = query.ToList();
                    gdv.DataBind();
                }

            }
        }

        public int countAppartenanceMembreOrganisation(long idmem, int cd)
        {
            int n = 0;
            using (Connection con = new Connection())
            {
                if (cd == 0)
                {
                    var b = (from l in con.MembresOrganisations
                             join a in con.Membres on l.MembreId equals a.MembreId
                             where l.MembreId==idmem
                             select l).Count();
                    n = b;
                }
               

            }
            return n;
        }


        public int Modifier(MembresOrganisation_Class membresOrganisationClass,long id)
        {
            using (Connection con = new Connection())
            {
                var mo = con.MembresOrganisations.FirstOrDefault(x => x.MembresOrganisationId == id);
                if (mo != null)
                {
                   
                    mo.Poste = membresOrganisationClass.Poste;
                    mo.name = membresOrganisationClass.Name;
                    mo.statut = membresOrganisationClass.Statut;
                    mo.RoleMembreID = membresOrganisationClass.RoleMembreID;

                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            con.MembresOrganisations.Add(mo);
                            con.Entry(mo).State = EntityState.Modified;
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public int Supprimer(long membresOrganisationId)
        {
            using (Connection con = new Connection())
            {
                var mo = con.MembresOrganisations.FirstOrDefault(x => x.MembresOrganisationId == membresOrganisationId);
                if (mo != null)
                {
                    con.MembresOrganisations.Remove(mo);
                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public int ChangerStatut(long membresOrganisationId, string statut)
        {
            using (Connection con = new Connection())
            {
                var mo = con.MembresOrganisations.FirstOrDefault(x => x.MembresOrganisationId == membresOrganisationId);
                if (mo != null)
                {
                    mo.statut = statut;
                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }
    }
}