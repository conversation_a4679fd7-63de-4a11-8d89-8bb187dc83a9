﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="formation.aspx.cs" Inherits="LinCom.formation" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
        <!-- Titre de la page -->
<div class="page-title">
  <div class="heading">
    <div class="container">
      <div class="row d-flex justify-content-center text-center">
        <div class="col-lg-8">
          <h2 class="display-5 fw-bold">Formations</h2>
          <p class="lead">Rejoignez notre communauté en tant que mentor ou mentoré et développez vos compétences et votre réseau.</p>
        </div>
      </div>
    </div>
  </div>
  <nav class="breadcrumbs">
    <div class="container">
      <ol>
        <li><a href="home.aspx">Accueil</a></li>
        <li class="current">Formation</li>
      </ol>
    </div>
  </nav>
</div>

<main class="main">
  <section class="events py-5 bg-light">
    <div class="container">
      <div class="row g-5">

        <!-- Colonne gauche : Programmes -->
        <div class="col-lg-8">
          <div class="row g-4">

            <!-- Exemple de carte de mentorat -->
          <div class="col-lg-12">
  <div class="card border-0 shadow rounded-4 overflow-hidden">
    <div class="card-body p-4">
      <h4 class="card-title text-dark fw-bold mb-3">
        Formation : Introduction à la Cybersécurité
      </h4>

      <p class="mb-2">
        <strong class="text-muted">Organisation :</strong> 
        <span class="text-dark">Institut Numérique Africain</span>
      </p>

      <p class="mb-2">
        <strong class="text-muted">Durée estimée :</strong> 
        <span class="text-dark">4 semaines</span>
      </p>

      <p class="mb-3">
        Cette formation vous initie aux bases de la cybersécurité, avec des modules interactifs pour comprendre les menaces, sécuriser vos appareils et protéger vos données personnelles.
      </p>

      <div class="mb-3">
        <strong class="text-muted">Modules :</strong>
        <ul class="list-group list-group-flush">
          <li class="list-group-item border-0 ps-0">
            <a href="#" class="text-decoration-none text-primary">
              🔒 Module 1 : Comprendre les menaces numériques
            </a>
          </li>
          <li class="list-group-item border-0 ps-0">
            <a href="#" class="text-decoration-none text-primary">
              🛡️ Module 2 : Sécuriser vos comptes et appareils
            </a>
          </li>
          <li class="list-group-item border-0 ps-0">
            <a href="#" class="text-decoration-none text-primary">
              📊 Module 3 : Confidentialité et protection des données
            </a>
          </li>
          <li class="list-group-item border-0 ps-0">
            <a href="#" class="text-decoration-none text-primary">
              💡 Module 4 : Pratiques sûres sur internet
            </a>
          </li>
        </ul>
      </div>

      <a href="#" class="btn text-white fw-semibold rounded-pill" style="background-color: #008374;">
        <i class="bi bi-pencil-square me-1"></i> S’inscrire à cette formation
      </a>
    </div>
  </div>
</div>

            <!-- Dupliquer d'autres cartes ici selon besoin -->

          </div>

          <!-- Pagination -->
          <nav class="mt-5 d-flex justify-content-center">
            <ul class="pagination pagination-rounded gap-2 mb-0">
              <li class="page-item"><a class="page-link" href="#"><i class="bi bi-chevron-left"></i></a></li>
              <li class="page-item active"><a class="page-link" href="#">1</a></li>
              <li class="page-item"><a class="page-link" href="#">2</a></li>
              <li class="page-item"><a class="page-link" href="#"><i class="bi bi-chevron-right"></i></a></li>
            </ul>
          </nav>
        </div>

        <!-- Colonne droite : Recherche + récents -->
        <div class="col-lg-4">
          <!-- Filtres -->
          <div class="card mb-4 shadow-sm rounded-4 p-3">
            <h5 class="fw-bold mb-3">Filtrer les formations</h5>
            <div class="d-grid gap-3">
              <select class="form-select rounded-pill">
                <option value="">Type de formation</option>
                <option>Leadership</option>
                <option>Entrepreneuriat</option>
                <option>Technologie</option>
              </select>
              <input type="month" class="form-control rounded-pill">
              <button class="btn btn-primary rounded-pill fw-semibold">Rechercher</button>
            </div>
          </div>

          <!-- Programmes récents -->
          <div class="card mb-4 shadow-sm rounded-4 p-3">
            <h5 class="fw-bold mb-3">Nouveaux formations</h5>
            <ul class="list-unstyled">
              <li class="mb-3">
                <a href="#" class="text-decoration-none text-dark">
                  <strong>Mentorat Femmes & Tech</strong><br>
                  <small class="text-muted"><i class="bi bi-calendar"></i> Mars 2025</small>
                </a>
              </li>
              <li class="mb-3">
                <a href="#" class="text-decoration-none text-dark">
                  <strong>Mentorat Rural Startups</strong><br>
                  <small class="text-muted"><i class="bi bi-calendar"></i> Février 2025</small>
                </a>
              </li>
            </ul>
          </div>

          <!-- Publicité -->
          <div class="bg-white shadow-sm p-4 rounded-4">
            <h5 class="fw-bold mb-3 text-center">Publicité</h5>
            <div id="carouselPubMentorat" class="carousel slide" data-bs-ride="carousel">
              <div class="carousel-inner rounded">
                <div class="carousel-item active">
                  <img src="assets/img/mentorat/skills.png" class="d-block w-100" alt="Publicité 1">
                </div>
                <div class="carousel-item">
                  <img src="assets/img/mentorat/skills.png" class="d-block w-100" alt="Publicité 2">
                </div>
              </div>
              <button class="carousel-control-prev" type="button" data-bs-target="#carouselPubMentorat" data-bs-slide="prev">
                <span class="carousel-control-prev-icon"></span>
              </button>
              <button class="carousel-control-next" type="button" data-bs-target="#carouselPubMentorat" data-bs-slide="next">
                <span class="carousel-control-next-icon"></span>
              </button>
            </div>
          </div>
        </div>

      </div>
    </div>
  </section>
</main>

</asp:Content>
