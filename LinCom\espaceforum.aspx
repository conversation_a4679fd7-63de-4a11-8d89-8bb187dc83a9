﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="espaceforum.aspx.cs" Inherits="LinCom.espaceforum" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

       <!-- Titre de la page -->
<div class="page-title">
  <div class="heading">
    <div class="container">
      <div class="row d-flex justify-content-center text-center">
        <div class="col-lg-8">
          <h2 class="display-5 fw-bold">Forum</h2>
          <p class="lead">Rejoignez notre communauté en tant que mentor ou mentoré et développez vos compétences et votre réseau.</p>
        </div>
      </div>
    </div>
  </div>
  <nav class="breadcrumbs">
    <div class="container">
      <ol>
        <li><a href="home.aspx">Accueil</a></li>
        <li class="current">Forum</li>
      </ol>
    </div>
  </nav>
</div>
   <main class="main">

  <!-- Section Forum -->
  <section class="forum-posts section">
    <div class="container">
      <div class="row">

        <!-- Colonne gauche : Sujets de discussion -->
        <div class="col-lg-8">

         
            <!-- Filtres -->
<div class="d-flex justify-content-between align-items-center mb-3">
  <h4 class="mb-0">Questions du Forum</h4>
  <div>
    <button class="btn btn-outline-primary btn-sm me-2" onclick="filterQuestions('recent')">Forums récents</button>
    <button class="btn btn-outline-secondary btn-sm" onclick="filterQuestions('unanswered')">Non répondus</button>
  </div>
</div>

          <div class="row gy-4">

            <!-- Sujet de forum -->
            <div class="col-md-12">
              <div class="forum-card">
                <div class="forum-header d-flex align-items-center">
                  <img src="assets/img/skills.png" class="user-avatar" alt="Avatar utilisateur">
                  <div class="user-info ms-2">
                    <h5 class="user-name">Jean K.</h5>
                    <small class="text-muted">Posté le <time datetime="2025-05-29">29 Mai 2025</time></small>
                  </div>
                </div>
                <div class="forum-body mt-2">
                  <p><strong>Sujet :</strong> <a href="#" class="forum-title">Comment impliquer les jeunes dans la gouvernance locale ?</a></p>
                  <p class="forum-snippet">Bonjour à tous ! Quels sont selon vous les meilleurs moyens pour intégrer les jeunes dans les décisions de leur commune ?...</p>
                </div>
                <div class="forum-footer">
                  <span class="text-muted"><i class="bi bi-chat-left-text"></i> 8 réponses</span>
                  <button class="btn btn-light btn-sm"><i class="bi bi-reply"></i> Répondre</button>
                  <button class="btn btn-light btn-sm"><i class="bi bi-share"></i> Partager</button>
                  <button class="btn btn-light btn-sm"><i class="bi bi-bell"></i> Suivre</button>
                </div>
              </div>
            </div>
            <!-- Fin Sujet -->
              <div class="col-md-12">
  <div class="forum-card">
    <div class="forum-header d-flex align-items-center">
      <img src="assets/img/skills.png" class="user-avatar" alt="Avatar utilisateur">
      <div class="user-info ms-2">
        <h5 class="user-name">Marie C.</h5>
        <small class="text-muted">Posté le <time datetime="2025-06-05">5 Juin 2025</time></small>
      </div>
    </div>
    <div class="forum-body mt-2">
      <p><strong>Sujet :</strong> <a href="#" class="forum-title">Comment encourager les femmes à s'engager en politique ?</a></p>
      <p class="forum-snippet">Bonjour à tous, j’aimerais connaître vos idées et expériences pour inciter plus de femmes à s’impliquer...</p>
    </div>
    <div class="forum-footer">
      <span class="text-muted"><i class="bi bi-chat-left-text"></i> 5 réponses</span>
      <button class="btn btn-light btn-sm"><i class="bi bi-reply"></i> Répondre</button>
      <button class="btn btn-light btn-sm"><i class="bi bi-share"></i> Partager</button>
      <button class="btn btn-light btn-sm"><i class="bi bi-bell"></i> Suivre</button>
    </div>
  </div>
</div>


            <!-- Tu peux dupliquer ce bloc pour chaque sujet -->

          </div>
        </div>

        <!-- Colonne droite : Filtrage -->
        <div class="col-lg-4">
         <div class="card mb-4 p-4 shadow-sm new-question-box">
  <h5 class="mb-3 d-flex align-items-center text-008374">
    <i class="bi bi-question-circle-fill me-2"></i> Poser une nouvelle question
  </h5>
  <form id="new-question-form">
    <div class="mb-3">
      <label for="question-title" class="form-label fw-semibold">Titre de la question</label>
      <input type="text" class="form-control input-custom" id="question-title"
        placeholder="Ex: Comment lancer un projet pour les jeunes ?">
    </div>
  
    <div class="mb-3">
      <label for="question-content" class="form-label fw-semibold">Contenu</label>
      <textarea class="form-control input-custom" id="question-content" rows="5"
        placeholder="Expliquez votre problème ou votre question..."></textarea>
    </div>
    <div class="text-end">
      <button type="submit" class="btn btn-008374 rounded-pill px-4">
        <i class="bi bi-send me-1"></i> Publier la question
      </button>
    </div>
  </form>
</div>

          <div class="search-sidebar p-3 bg-light rounded shadow-sm">
                          <!-- Formulaire de nouvelle question -->

            <h5>Filtrer les discussions</h5>
            <form id="forumFilterForm">
              <div class="mb-3">
                <label for="categoryFilter" class="form-label">Catégorie</label>
                <select id="categoryFilter" class="form-select">
                  <option value="">Toutes</option>
                  <option value="Jeunesse">Jeunesse</option>
                  <option value="Santé">Santé</option>
                  <option value="Éducation">Éducation</option>
                </select>
              </div>
              <div class="mb-3">
                <label for="dateFilter" class="form-label">Date</label>
                <input type="date" id="dateFilter" class="form-control">
              </div>
              <div class="mb-3">
                <label for="authorFilter" class="form-label">Auteur</label>
                <input type="text" id="authorFilter" class="form-control" placeholder="Nom de l’auteur">
              </div>
              <button type="submit" class="btn btn-success w-100">Appliquer</button>
            </form>
          </div>
        </div>

      </div>
    </div>
  </section>

  <!-- Pagination -->
  <!-- Pagination -->
    <nav class="d-flex justify-content-center mt-4">
      <ul class="pagination pagination-sm">
        <li class="page-item"><a class="page-link text-008374" href="#"><i class="bi bi-chevron-left"></i></a></li>
        <li class="page-item active"><a class="page-link bg-008374 border-0" href="#">1</a></li>
        <li class="page-item"><a class="page-link text-008374" href="#">2</a></li>
        <li class="page-item"><a class="page-link text-008374" href="#"><i class="bi bi-chevron-right"></i></a></li>
      </ul>
    </nav>
</main>

<style>
.forum-card {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  font-size: 0.9rem;
}

.forum-header .user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.user-info .user-name {
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
}

.forum-title {
  font-weight: 600;
  color: #0a66c2;
  text-decoration: none;
}

.forum-title:hover {
  text-decoration: underline;
}

.forum-snippet {
  color: #444;
  font-size: 0.88rem;
  margin-top: 5px;
}

.forum-footer {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 10px;
  font-size: 0.85rem;
}

.forum-footer .btn {
  padding: 4px 10px;
  border: 1px solid #ddd;
}

.search-sidebar {
  background: #f9f9f9;
  max-height: 80vh;
  overflow-y: auto;
}

.search-sidebar h5 {
  margin-bottom: 15px;
}
/* Couleur personnalisée */
.text-008374 {
  color: #008374;
}
.btn-008374 {
  background-color: #008374;
  color: white;
  border: none;
}
.btn-008374:hover {
  background-color: #006e63;
  color: white;
}

/* Champ avec un style doux */
.input-custom {
  border-radius: 8px;
  border: 1px solid #ccc;
  transition: all 0.2s;
}
.input-custom:focus {
  border-color: #008374;
  box-shadow: 0 0 0 0.2rem rgba(0, 131, 116, 0.2);
}

/* Carte plus moderne */
.new-question-box {
  background: #ffffff;
  border-left: 5px solid #008374;
  border-radius: 12px;
}

</style>
</asp:Content>
