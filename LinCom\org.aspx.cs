﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.file;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class org : System.Web.UI.Page
    {
        Organisation_Class og = new Organisation_Class();
        Organisation_Class orga = new Organisation_Class();
        IOrganisation objorg = new OrganisationImp();
        IPoste objpost = new PosteImp();
        Post_Class post = new Post_Class();
        Post_Class pos = new Post_Class();
        IDomainePost objdompost = new DomainePostImp();
        DomainePost_Class dompost = new DomainePost_Class();
        IDomaineIntervention objdomi = new DomaineInterventionImp();
        DomaineIntervention_Class domi = new DomaineIntervention_Class();

        IDomaineInterventionOrganisation objdomorg = new DomaineInterventionOrganisationImp();
        DomaineInterventionOrganisation_Class domorg = new DomaineInterventionOrganisation_Class();

        Membre_Class mem = new Membre_Class();
        IMembre objmem = new MembreImp();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();

        IPartenaire objpart = new PartenaireImp();
        Partenaire_Class part = new Partenaire_Class();
        static string nsco;
        private int PageSize = 2;

        protected void Page_Load(object sender, EventArgs e)
        {
            nsco = Request.QueryString["name"];

            if (!IsPostBack)
            {
                affich();

                if (nsco == null)
                    Response.Redirect("~/ong.aspx");
            }

        }
        void affich()
        {
            objorg.AfficherDetails(nsco, og);
            txtnm.InnerHtml = og.Nom;
            txtnm1.InnerHtml = og.Nom;
            txtnm2.InnerHtml = og.Nom;
            imgorg.Src = "~/file/organ/" + og.Logo;
            txtsigle.InnerHtml = og.sigle;
            txtsiteweb.HRef = og.SiteWeb;
            txtsiteweb.InnerHtml = og.SiteWeb;
            txtsiteweb1.HRef = og.SiteWeb;
            txtsiteweb1.InnerHtml = og.SiteWeb;
            txtsiteweb2.InnerHtml = og.SiteWeb;
            txtfacebook.HRef = og.facebook;
            txttwitter.HRef = og.twitter;
            txtyoutube.HRef = og.youtube;
            txtlinkedin.HRef = og.linkedin;

            txtphone.InnerHtml = og.Telephone;
            txtemail.InnerText = og.Email;
            txtadresse.InnerText = og.Adresse;
            txtdescription.InnerHtml = og.Description;

            txtvision.InnerHtml = og.Vision;
            txtmission.InnerHtml = og.Mission;

            objdomorg.ChargerListviewDomainesInterventionOrganisation(listdomaine,og.OrganisationId);
            objpost.Chargement_GDV(listprojet, -1, og.OrganisationId, "projet", "publié", 0);
            objpost.Chargement_GDV(listprojet, -1, og.OrganisationId, "article", "publié", 0);
            objpost.Chargement_GDV(listprojet, -1, og.OrganisationId, "service", "publié", 0);

            objmemorg.ChargerMembresListview(listmembre,"",0,"actif",og.OrganisationId);

            //objpost.Chargement_GDV(listpost, -1, -1, "article", "publié", 1);
            //objpost.Chargement_GDV(listevent, -1, -1, "evenement", "publié", 1);
            //objpart.Chargement_GDV(listpartenaire, "publié");


        }
        protected void listinst_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            if (e.Item.ItemType == ListViewItemType.DataItem)
            {
                // Récupérer l'organisation courante
                //  DataRowView drv = (DataRowView)e.Item.DataItem;
                //  long organisationId = Convert.ToInt64(drv["id"]);

                dynamic organisation = e.Item.DataItem;
                long id = organisation.id;


                // Trouver le Repeater des domaines
                Repeater rptDomaines = (Repeater)e.Item.FindControl("rptDomaines");
                if (rptDomaines != null)
                {
                    objdomorg.ChargerRepeaterDomainesInterventionOrganisation(rptDomaines, id);

                }
            }
        }


        protected void btnsearch_ServerClick(object sender, EventArgs e)
        {
           
        }

        protected void listactivite_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            string index = e.CommandArgument.ToString();
            if (e.CommandName == "view" )
            {

                objpost.AfficherDetailsname(index, -1, 1, pos);
                post.number_of_view = pos.number_of_view + 1;
                objpost.MiseajourData(post, pos.PostId, -1, 0);

                Response.Redirect("~/details.aspx?name=" + index);



            }
        }
    }
}