﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IMessage
    {
        void AfficherDetails(long messageId, Message_Class message);
        int Envoyer(Message_Class message);
        int Modifier(Message_Class message);
        int Supprimer(long messageId);
        void ChargerMessages(Repeater rpt, long conversationId, int nombreMessages = 50);
        int CompterNonLus(long membreId);
       
    }
}
