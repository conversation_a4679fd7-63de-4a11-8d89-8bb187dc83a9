﻿using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IReplyForum
    {
        int add(ReplyForum_Class add);
        void Chargement_GDV(GridView GV_apv);
        void search(GridView GV_apv, string code);
        void afficherDetails(int code, ReplyForum_Class pr);
        void afficherDetails(string code, ReplyForum_Class pr);
        int edit(ReplyForum_Class cl, int id);
        int supprimer(int id);

        void chargerReplyFroum(DropDownList lst);
        int count();

       
    }
}
